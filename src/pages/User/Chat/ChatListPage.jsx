import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { SkeletonLoader } from "Components/Skeleton";
import { useNotificationTrigger } from "../../../hooks/useNotificationTrigger";
import { useNotifications } from "../../../context/Notifications/NotificationContext";

const ChatListPage = () => {
  const navigate = useNavigate();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const { checkForNewChats } = useNotificationTrigger();
  const { clearNotification } = useNotifications();
  const [chats, setChats] = useState([
    {
      id: { value: 1 },
      name: { value: "<PERSON>" },
      last_message: { value: "Hi, I'm interested in the project position you sent." },
      created_at: { value: "2024-02-08T11:05:00" },
      unread: { value: true }
    },
    {
      id: { value: 2 },
      name: { value: "<PERSON>" },
      last_message: { value: "Thanks for the referral!" },
      created_at: { value: "2024-02-08T10:30:00" },
      unread: { value: false }
    },
    {
      id: { value: 3 },
      name: { value: "Emma Wilson" },
      last_message: { value: "When would be a good time for our next meeting..." },
      created_at: { value: "2024-02-08T09:45:00" },
      unread: { value: false }
    }
  ]);

  useEffect(() => {
    loadChats();

    // Set up polling for new chats
    const interval = setInterval(() => {
      console.log('[Notifications] Polling for new chats...');
      loadChats();
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Clear chat notifications when component mounts
  useEffect(() => {
    clearNotification('chats');
  }, [clearNotification]);

  const loadChats = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.GetChats();

      if (!response.error && response.list?.length > 0) {
        setChats(response.list);
        // Check for new chats and trigger notifications
        console.log(`[Notifications] Checking chats: ${response.list.length} items`);
        checkForNewChats(response.list);
      }
    } catch (err) {
      setError(err.message);
      setTimeout(() => setError(""), 5000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e]">
      <div className="mx-auto max-w-3xl p-4">
        {error && <Toast message={error} type="error" />}

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-[#eaeaea]">Messages</h1>
          <p className="text-[#b5b5b5]">Your conversations</p>
        </div>

        <div className="space-y-2">
          {loading ? (
            [...Array(3)].map((_, i) => (
              <SkeletonLoader key={i} className="h-20 w-full rounded-lg" />
            ))
          ) : (
            chats.map((chat) => (
              <button
                key={chat.id.value}
                onClick={() => navigate(`/member/chat/${chat.id.value}`)}
                className="flex w-full items-center gap-4 rounded-lg bg-[#242424] p-4 text-left transition-all hover:bg-[#363636]"
              >
                <div className="relative">
                  <div className="h-12 w-12 rounded-full bg-[#363636]" />
                  {chat.unread.value && (
                    <span className="absolute right-0 top-0 h-3 w-3 rounded-full bg-red-500" />
                  )}
                </div>
                <div className="flex-1 overflow-hidden">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-[#eaeaea]">{chat.partner?.name?.value || chat.name?.value}</h3>
                    <span className="text-xs text-[#b5b5b5]">
                      {new Date(chat.last_message_time?.value || chat.created_at?.value).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  <p className="truncate text-sm text-[#b5b5b5]">
                    {chat.last_message.value}
                  </p>
                </div>
              </button>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatListPage; 